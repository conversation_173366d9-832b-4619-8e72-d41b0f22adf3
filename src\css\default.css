:root {
  --bg-rotation-degree: 258deg;
}

.main-nowPlayingView-contextItemInfo::before {
  display: none;
}

#SpicyLyricsPage {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
  height: 100%;
  container-type: size;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  font-weight: 700;
  z-index: 99;
  user-select: none;
}

#SpicyLyricsPage {
  font-family: "Noto Sans Display", sans-serif;
}

[font="Vazirmatn"] {
  font-family: "Vazirmatn", sans-serif;
}

body:has(#SpicyLyricsPage) .main-view-container__scroll-node-child,
body:has(#SpicyLyricsPage) .main-view-container__scroll-node-child-spacer,
body:has(#SpicyLyricsPage) .main-view-container__scroll-node-child,
body:has(#SpicyLyricsPage) .main-view-container__scroll-node-child-spacer {
  display: none;
}

#SpicyLyricsPage .ViewControls {
  container-type: size;
  display: flex;
  gap: 8px;
  height: 5cqh;
  justify-content: center;
  opacity: .5;
  transition: opacity .2s, bottom .5s;
  z-index: 101;
  --ViewControlSize: 100cqh;
}

#SpicyLyricsPage:not(.Fullscreen) .ViewControls {
  position: absolute;
  width: 100cqw;
  bottom: -8cqh;
  --PageHoverOffset: -2.2cqh;
  --ControlsHoverOffset: 1.5cqh;
}

#SpicyLyricsPage .ViewControls .ViewControl {
  --ViewControlHeight: var(--ViewControlSize, 100cqh);
  aspect-ratio: 1;
  background: #ffffff36;
  border: none;
  border-radius: 100rem;
  display: flex;
  height: var(--ViewControlHeight);
  width: var(--ViewControlHeight);
  -webkit-app-region: no-drag;
  align-items: center;
  color: white;
  justify-content: center;
  text-align: center;
  -webkit-box-align: center;
  -webkit-box-pack: center
}

#SpicyLyricsPage .ViewControls .ViewControl:hover {
  background:rgba(0,0,0,.3)
}

#SpicyLyricsPage .ViewControls .ViewControl svg {
  fill: currentColor;
}

#SpicyLyricsPage:not(.Fullscreen) .ViewControls:hover {
  opacity: 1 !important;
  bottom: var(--ControlsHoverOffset) !important;
}

#SpicyLyricsPage:not(.Fullscreen):hover .ViewControls {
  opacity: .5;
  bottom: var(--PageHoverOffset);
}

#SpicyLyricsPage .ViewControls button {
  cursor: pointer
}


.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) {
  --background-tint: color-mix(in srgb,rgb(var(--spice-rgb-selected-row)) 7%,transparent);
  --spice-card: var(--background-tint);
  --background-tinted-base: var(--background-tint)
}

.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) .main-nowPlayingView-content {
  background: transparent
}

.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) .main-nowPlayingView-contextItemInfo:before {
  display: none
}

.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) .sweet-dynamic-bg div[data-overlayscrollbars-viewport],.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) .sweet-dynamic-bg div[data-overlayscrollbars-viewport]>div {
  background: transparent
}


.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) .sweet-dynamic-bg .main-nowPlayingView-coverArtContainer div:has(video):after,.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) .sweet-dynamic-bg .main-nowPlayingView-coverArtContainer div:has(video):before {
  display: none
}

.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) .sweet-dynamic-bg .main-trackInfo-artists {
  filter: brightness(1.15);
  opacity: .75
}

.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) .main-nowPlayingView-coverArt {
  box-shadow: 0 9px 20px 0 rgba(0,0,0,.271);
  opacity: .95
}

.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) .main-nowPlayingView-section {
  background-color: var(--background-tinted-base)
}

.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) button[type=button] {
  background-color: var(--background-tinted-base);
  color: hsla(0,0%,100%,.8)
}

.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) button[type=button] .Button-sm-buttonSecondary-isUsingKeyboard-useBrowserDefaultFocusStyle,.Root__right-sidebar:has(.main-nowPlayingView-section,canvas) button[type=button] .Button-sm-buttonSecondary-useBrowserDefaultFocusStyle {
  border: 1px solid hsla(0,0%,100%,.5)
}

#SpicyLyricsPageSvg {
  fill: currentColor;
  transform: translateY(2px);
}

button:has(#SpicyLyricsPageSvg):after {
  transform: translateX(-370%) translateY(-40%) !important;
}

/* "Page Not Visible" fix */
.Root__main-view:has(#SpicyLyricsPage),
.Root__main-view:has(#SpicyLyricsPage) .KL8t9WB65UfUEPuTFAhO,
.Root__main-view:has(#SpicyLyricsPage) .main-content-view,
.Root__main-view:has(#SpicyLyricsPage) .main-view-container,
.Root__main-view:has(#SpicyLyricsPage) .main-view-container__scroll-node,
.Root__main-view:has(#SpicyLyricsPage) .main-view-container .div[data-overlayscrollbars-viewport] {
  height: 100% !important;
}