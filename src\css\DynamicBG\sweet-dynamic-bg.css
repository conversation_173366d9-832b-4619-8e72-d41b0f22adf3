/* 
 * Optimized Dynamic Background
 * - Uses fewer DOM elements
 * - Leverages CSS gradients for better performance
 * - Optimizes GPU usage with proper transform properties
 * - Reduces blur operations which are CPU intensive
 */

.sweet-dynamic-bg {
  /* Base variables */
  --bg-hue-shift: 0deg;
  --bg-saturation: 1.5;
  --bg-brightness: 0.6; /* Reduced from 0.8 to make it dimmer */
  
  /* Rotation variables for different elements */
  --bg-rotation-primary: 0deg;
  --bg-rotation-secondary: 0deg;
  --bg-scale-primary: 1;
  --bg-scale-secondary: 1;
  
  height: 100%;
  left: 0;
  overflow: hidden;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  
  /* Apply container-level filters instead of per-element */
  filter: saturate(var(--bg-saturation)) brightness(var(--bg-brightness));
  
  /* Create stacking context for better compositing */
  isolation: isolate;
  
  /* Optimize for animation */
  will-change: transform;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Add a semi-transparent overlay for consistent dimming */
.sweet-dynamic-bg::after {
  content: '';
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.2); /* Black overlay with 20% opacity */
  z-index: 10; /* Above all other elements in the dynamic bg */
  pointer-events: none;
}

/* Lightweight placeholder that appears instantly */
.sweet-dynamic-bg .placeholder {
  position: absolute;
  inset: 0;
  background: radial-gradient(
    circle at center,
    rgba(40, 40, 40, 0.7) 0%,
    rgba(20, 20, 20, 0.3) 70%
  );
  z-index: 1;
  border-radius: 50%;
  transform: scale(2);
  opacity: 0.8;
}

/* Base style for background images */
.sweet-dynamic-bg > img.bg-image {
  position: absolute;
  width: 200%;
  height: 200%;
  border-radius: 100em;
  opacity: 0; /* Start hidden */
  transition: opacity 1s ease-in-out; /* Transition for crossfade */
  will-change: transform, opacity;
  transform-style: flat;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Primary background image styling */
.sweet-dynamic-bg > img.primary {
  position: absolute;
  right: 0;
  top: 0;
  width: 200%;
  height: 200%;
  border-radius: 100em;
  z-index: 3;
  transform: rotate(var(--bg-rotation-primary, 0deg)) scale(var(--bg-scale-primary, 1));
  filter: blur(25px) hue-rotate(var(--bg-hue-shift));
  animation: bgAnimPrimary 60s linear infinite;
}

/* Secondary background image styling */
.sweet-dynamic-bg > img.secondary {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 200%;
  border-radius: 100em;
  z-index: 2;
  transform: rotate(var(--bg-rotation-secondary, 0deg)) scale(var(--bg-scale-secondary, 1));
  filter: blur(45px) hue-rotate(calc(var(--bg-hue-shift) + 30deg));
  animation: bgAnimSecondary 75s linear infinite reverse;
}

/* Active state for crossfade */
.sweet-dynamic-bg > img.primary.active {
  opacity: 1; /* Primary is fully opaque when active */
}

.sweet-dynamic-bg > img.secondary.active {
  opacity: 0.8; /* Secondary is slightly transparent when active */
}


/* Fullscreen mode optimizations */
#SpicyLyricsPage.Fullscreen .sweet-dynamic-bg {
  max-height: 60%;
  max-width: 20%;
  scale: 500% 170%; /* Keep existing scaling */
}

.sweet-dynamic-bg-in-this {
  overflow: hidden;
  position: relative;
}
/* Specific styling for sidebar */
.sweet-dynamic-bg-in-this:is(aside) .sweet-dynamic-bg {
  --bg-saturation: 2;
  --bg-brightness: 0.45;
  max-height: 100%;
  max-width: 100%;
}
/* Video element styling (Keep as is) */
.sweet-dynamic-bg-in-this:is(aside) video {
  filter: opacity(0.75) brightness(0.5);
  -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 35%, rgba(0, 0, 0, 0) 90%);
  mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 35%, rgba(0, 0, 0, 0) 90%);
}
/* Remove default styling from video containers (Keep as is) */
.main-nowPlayingView-coverArtContainer div:has(video)::before,
.main-nowPlayingView-coverArtContainer div:has(video)::after {
  display: none;
}
/* Ensure content stays above background (Keep as is) */
.sweet-dynamic-bg-in-this:is(aside) .AAdBM1nhG73supMfnYX7 {
  z-index: 10;
  position: relative;
}
/* Lyrics page specific styling */
#SpicyLyricsPage .sweet-dynamic-bg {
  --bg-saturation: 2.5;
  --bg-brightness: 0.45;
  max-height: 55%;
  max-width: 35%;
  scale: 290% 185%;
  transform-origin: left top; /* Keep existing transform origin */
}

/* Optimized animation keyframes for primary element (Keep as is) */
@keyframes bgAnimPrimary {
  0% {
    transform: rotate(var(--bg-rotation-primary, 0deg)) scale(var(--bg-scale-primary, 1));
  }
  to {
    transform: rotate(calc(var(--bg-rotation-primary, 0deg) + 1turn)) scale(var(--bg-scale-primary, 1));
  }
}
/* Optimized animation keyframes for secondary element (Keep as is) */
@keyframes bgAnimSecondary {
  0% {
    transform: rotate(var(--bg-rotation-secondary, 0deg)) scale(var(--bg-scale-secondary, 1));
  }
  to {
    transform: rotate(calc(var(--bg-rotation-secondary, 0deg) + 1turn)) scale(var(--bg-scale-secondary, 1));
  }
}
/* Performance optimization for fullscreen mode (Keep as is) */
body:has(#SpicyLyricsPage.Fullscreen) .Root__right-sidebar aside:is(.NowPlayingView, .sweet-dynamic-bg-in-this) .sweet-dynamic-bg,
body:has(#SpicyLyricsPage.Fullscreen) .Root__right-sidebar aside:is(.NowPlayingView, .sweet-dynamic-bg-in-this) .sweet-dynamic-bg * {
  display: none !important;
  animation: none !important;
  filter: none !important;
}
/* Media query for lower-end devices */
@media (prefers-reduced-motion), (max-width: 768px) {
  .sweet-dynamic-bg {
    --bg-saturation: 1.2;
    --bg-brightness: 0.5;
  }

  /* Adjust animation/filter directly on the image elements */
  .sweet-dynamic-bg > img.primary {
    animation-duration: 120s; /* Slower animation */
    filter: blur(20px) hue-rotate(var(--bg-hue-shift)); /* Adjusted blur */
  }

  .sweet-dynamic-bg > img.secondary {
    animation-duration: 120s; /* Slower animation */
    filter: blur(20px) hue-rotate(calc(var(--bg-hue-shift) + 30deg)); /* Adjusted blur */
  }

  /* No need to disable canvas as it's removed */
}
