#SpicyLyricsPage [data-simplebar] {
  position: relative;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start;
}

#SpicyLyricsPage .simplebar-wrapper {
  overflow: hidden;
  width: inherit;
  height: inherit;
  max-width: inherit;
  max-height: inherit;
}

#SpicyLyricsPage .simplebar-mask {
  direction: inherit;
  position: absolute;
  overflow: hidden;
  padding: 0;
  margin: 0;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  width: auto !important;
  height: auto !important;
  z-index: 0;
}

#SpicyLyricsPage .simplebar-offset {
  direction: inherit !important;
  box-sizing: inherit !important;
  resize: none !important;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  padding: 0;
  margin: 0;
  -webkit-overflow-scrolling: touch;
}

#SpicyLyricsPage .simplebar-content-wrapper {
  direction: inherit;
  box-sizing: border-box !important;
  position: relative;
  display: block;
  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */
  width: auto;
  max-width: 100%; /* Not required for horizontal scroll to trigger */
  max-height: 100%; /* Needed for vertical scroll to trigger */
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

#SpicyLyricsPage .simplebar-content-wrapper::-webkit-scrollbar,
#SpicyLyricsPage .simplebar-hide-scrollbar::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

#SpicyLyricsPage .simplebar-content:before,
#SpicyLyricsPage .simplebar-content:after {
  content: ' ';
  display: table;
}

#SpicyLyricsPage .simplebar-placeholder {
  max-height: 100%;
  max-width: 100%;
  width: 100%;
  pointer-events: none;
}

#SpicyLyricsPage .simplebar-height-auto-observer-wrapper {
  box-sizing: inherit !important;
  height: 100%;
  width: 100%;
  max-width: 1px;
  position: relative;
  float: left;
  max-height: 1px;
  overflow: hidden;
  z-index: -1;
  padding: 0;
  margin: 0;
  pointer-events: none;
  flex-grow: inherit;
  flex-shrink: 0;
  flex-basis: 0;
}

#SpicyLyricsPage .simplebar-height-auto-observer {
  box-sizing: inherit;
  display: block;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 1000%;
  width: 1000%;
  min-height: 1px;
  min-width: 1px;
  overflow: hidden;
  pointer-events: none;
  z-index: -1;
}

#SpicyLyricsPage .simplebar-track {
  z-index: 1;
  position: absolute;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

#SpicyLyricsPage [data-simplebar].simplebar-dragging {
  pointer-events: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

#SpicyLyricsPage [data-simplebar].simplebar-dragging .simplebar-content {
  pointer-events: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

#SpicyLyricsPage [data-simplebar].simplebar-dragging .simplebar-track {
  pointer-events: all;
}

#SpicyLyricsPage .simplebar-scrollbar {
  position: absolute;
  left: 0;
  right: 0;
  min-height: 10px;
}

.simplebar-scrollbar:before {
  position: absolute;
  content: '';
  background: var(--Simplebar-Scrollbar-Color) !important;
  border-radius: 7px;
  left: 2px;
  right: 2px;
  opacity: 0;
  transition: opacity 0.2s 0.5s linear;
}

#SpicyLyricsPage .simplebar-scrollbar.simplebar-visible:before {
  opacity: 0.5 !important;
  transition-delay: 0s !important;
  transition-duration: 0s !important;
}

#SpicyLyricsPage .simplebar-track.simplebar-vertical {
  top: 0;
  width: 11px;
}

#SpicyLyricsPage .simplebar-scrollbar:before {
  top: 2px;
  bottom: 2px;
  left: 2px;
  right: 2px;
}

#SpicyLyricsPage .simplebar-track.simplebar-horizontal {
  left: 0;
  height: 11px;
}

#SpicyLyricsPage .simplebar-track.simplebar-horizontal .simplebar-scrollbar {
  right: auto;
  left: 0;
  top: 0;
  bottom: 0;
  min-height: 0;
  min-width: 10px;
  width: auto;
}

/* Rtl support */
#SpicyLyricsPage [data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {
  right: auto;
  left: 0;
}

#SpicyLyricsPage .simplebar-dummy-scrollbar-size {
  direction: rtl;
  position: fixed;
  opacity: 0;
  visibility: hidden;
  height: 500px;
  width: 500px;
  overflow-y: hidden;
  overflow-x: scroll;
  -ms-overflow-style: scrollbar !important;
}

#SpicyLyricsPage .simplebar-dummy-scrollbar-size > div {
  width: 200%;
  height: 200%;
  margin: 10px 0;
}

#SpicyLyricsPage .ScrollbarScrollable .simplebar-track {
  transition: opacity 0.2s linear;
  opacity: 1;
}

#SpicyLyricsPage .ScrollbarScrollable .simplebar-track.simplebar-vertical {
  right: 5px
}

#SpicyLyricsPage .ScrollbarScrollable .simplebar-track.simplebar-horizontal {
  bottom: 5px
}

#SpicyLyricsPage .ScrollbarScrollable.hide-scrollbar .simplebar-track {
  opacity: 0;
}