# Amai Lyrics - Product Overview

Amai Lyrics is a Spicetify extension that enhances the Spotify experience for Japanese and Korean music listeners. It provides:

- **Line-by-line translations** using Google's Gemini AI
- **Furigana** (pronunciation guides) for Japanese Kanji characters
- **Romaji** (Latin script) for Japanese lyrics
- **Korean Romanization** for Korean lyrics
- **Multi-language support** for translations (English, Spanish, French, German, Portuguese, Chinese, Thai, Indonesian, Malay, Japanese, Korean)

## Key Features

- Seamless integration with Spotify's lyrics interface
- Dynamic background effects synchronized with album artwork
- Clean, readable design optimized for music consumption
- Automatic caching and performance optimization
- User-configurable API key setup through Spotify settings

## Target Users

Music enthusiasts who enjoy Japanese and Korean songs but need assistance with reading/understanding the lyrics.

## Distribution

Released as a single JavaScript file (`amai-lyrics-main.js`) that users install into their Spicetify extensions folder.
