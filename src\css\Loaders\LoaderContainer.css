
#SpicyLyricsPage .LyricsContainer .loaderContainer {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  inset: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: -1;
  opacity: 0;
  transition: all 0.4s ease-in-out;
}

#SpicyLyricsPage .LyricsContainer .loaderContainer.active {
  position: relative;
  opacity: 1;
  z-index: 9;
}

#SpicyLyricsPage .LyricsContainer .loaderContainer:is(.active) + .LyricsContent {
  display: none;
}
