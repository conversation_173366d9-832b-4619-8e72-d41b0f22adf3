.Skeletoned {
    /* This part was made by @surfbryce, that made the Beautiful Lyrics extension */
    --BorderRadius: .5cqw;
    --ValueStop1: 40%;
    --ValueStop2: 50%;
    --ValueStop3: 60%;
    --ColorStop1: hsla(0, 0%, 93%, .25);
    --ColorStop2: hsla(0, 0%, 98%, .45);
    --ColorStop3: hsla(0, 0%, 93%, .25);
    animation: skeleton 1s linear infinite;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background: linear-gradient(-45deg, var(--ColorStop1) var(--ValueStop1), var(--ColorStop2) var(--ValueStop2), var(--ColorStop3) var(--ValueStop3));
    background-position-x: 100%;
    background-size: 500%;
    border-radius: var(--BorderRadius);
}

.Skeletoned * {
    display: none;
}

@keyframes skeleton {
    to {
        background-position-x: 0
    }
}

@keyframes vinyl-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

#SpicyLyricsPage .ContentBox {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    --default-font-size: clamp(0.5rem, calc(0.8cqw * 3), 3rem);
    --songname-font-size: clamp(0.5rem, calc(0.7cqw * 3), 3rem);
}

#SpicyLyricsPage .ContentBox .NowBar {
    --title-height: 5cqh;
    display: flex;
    position: absolute;
    inset: 0;
    align-items: center;
    justify-content: center;
    height: 100cqh;
    z-index: -1;
    margin: 0 3.5cqw 0 3.5cqw;
    transition: opacity 0.2s ease-in-out;
    opacity: 0;
}

#SpicyLyricsPage .ContentBox .NowBar.RightSide {
    margin: 0 3.5cqw 0 3.5cqw;
}

#SpicyLyricsPage .ContentBox .NowBar.Active {
    position: relative;
    opacity: 1;
    z-index: 1;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 20cqw;
    height: 20cqw;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: 
        radial-gradient(circle at center, transparent 15%, rgba(255,255,255,0.02) 15.5%, transparent 16%),
        radial-gradient(circle at center, transparent 20%, rgba(255,255,255,0.02) 20.5%, transparent 21%),
        radial-gradient(circle at center, transparent 25%, rgba(255,255,255,0.02) 25.5%, transparent 26%),
        radial-gradient(circle at center, transparent 30%, rgba(255,255,255,0.02) 30.5%, transparent 31%),
        radial-gradient(circle at center, transparent 35%, rgba(255,255,255,0.02) 35.5%, transparent 36%),
        radial-gradient(circle at center, transparent 40%, rgba(255,255,255,0.02) 40.5%, transparent 41%),
        radial-gradient(circle at center, transparent 45%, rgba(255,255,255,0.02) 45.5%, transparent 46%),
        radial-gradient(circle at center, #1a1a1a 0%, #0d0d0d 70%, #000000 100%);
    z-index: 1;

    box-shadow: 
        inset 0 0 20px rgba(0,0,0,0.8),
        0 9px 20px 0 rgba(0, 0, 0, .271);
}

#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox::after {
    content: '';
    position: absolute;
    width: 8%;
    height: 8%;
    border-radius: 50%;
    background: radial-gradient(circle at center, #000000 0%, #1a1a1a 50%, #333333 100%);
    z-index: 4;
    box-shadow: 
        inset 0 0 8px rgba(0,0,0,0.9),
        0 2px 6px rgba(0,0,0,0.5);
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .Header .MediaBox {
    width: 25cqw;
    height: 25cqw;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox .MediaImagePlaceholder {
    position: absolute;
    border-radius: 50%;
    width: 42%;
    height: 42%;
    background-color: rgba(30, 30, 30, 0.3);
    z-index: 2;
    transition: opacity 0.3s ease-in-out;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox .MediaImage.loaded + .MediaImagePlaceholder,
#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox:has(.MediaImage.loaded) .MediaImagePlaceholder {
    opacity: 0;
}

#SpicyLyricsPage .ContentBox .NowBar .AmaiPageButtonContainer {
    display: flex;
    flex-direction: column;
    gap: 8px; /* Adjust this value as needed for desired spacing */
}

#SpicyLyricsPage .ContentBox .NowBar .AmaiPageButtonContainer .AmaiPageButton {
    width: 24cqh;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox .MediaImage {
    --ArtworkBrightness: 1;
    --ArtworkBlur: 0px;
    border-radius: 50%;
    width: 42%;
    height: 42%;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, .4);
    opacity: .95;
    transition: opacity, scale .1s cubic-bezier(.24,.01,.97,1.41);
    cursor: grab;
    z-index: 3;
    position: absolute;
    will-change: transform;
    contain: paint layout;
    animation: vinyl-spin 12s linear infinite;
    
    /* Start with simpler rendering until loaded */
    filter: brightness(var(--ArtworkBrightness));
}

/* Apply full styling only after image has loaded */
#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox .MediaImage.loaded {
    filter: brightness(var(--ArtworkBrightness)) blur(var(--ArtworkBlur));
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .Header .MediaBox .MediaContent {
    cursor: grab;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .Header .MediaBox .MediaContent .AlbumData {
    font-size: calc(var(--default-font-size) * 0.95);
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    z-index: 99;
    /* margin-bottom: 1cqh; */
    opacity: 1;
    color: white;
    margin-top: 15.5cqh;
    overflow: hidden;
    width: 20cqw;
    box-sizing: border-box;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .Header .MediaBox .MediaContent .AlbumData span {
    width: 100%;
    white-space: nowrap;          /* Prevents text from wrapping */
    overflow: hidden;             /* Hides overflowing text */
    text-overflow: ellipsis;      /* Adds the ellipsis (...) */
    display: inline-block;        /* Ensures width is respected */
    text-align: center;           /* Keeps text centered */
}

/* #SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox:hover .MediaImage {
    opacity: 1;
    scale: 1.01;
} */

#SpicyLyricsPage .ContentBox .NowBar .Header .Metadata {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin: 2cqh;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .Metadata .SongName {
    font-weight: 700;
    font-size: var(--songname-font-size);
    color: white;
    text-align: center;           /* Fallback for child alignment */
    opacity: .95;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .Metadata .SongName span {
    white-space: nowrap;          /* Prevents text from wrapping */
    overflow: hidden;             /* Hides overflowing text */
    text-overflow: ellipsis;      /* Adds the ellipsis (...) */
    max-width: 20cqw;              /* Restricts span to the container width */
    display: inline-block;        /* Ensures width is respected */
    text-align: center;           /* Align text within the span */
    line-height: var(--title-height);
}

#SpicyLyricsPage .ContentBox .NowBar .Header .Metadata .Artists {
    font-size: calc(var(--default-font-size)* 0.65);
    line-height: calc(var(--title-height) * 0.65);
    font-weight: 400;
    color: white;
    opacity: .7;
    animation: none;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .Metadata .Artists span {
    white-space: nowrap;          /* Prevents text from wrapping */
    overflow: hidden;             /* Hides overflowing text */
    text-overflow: ellipsis;      /* Adds the ellipsis (...) */
    max-width: 20cqw;             /* Restricts span to the container width */
    display: inline-block;        /* Ensures width is respected */
    text-align: center;           /* Align text within the span */
}

#SpicyLyricsPage .ContentBox .NowBar:is(.Active.LeftSide) + .LyricsContainer .loaderContainer {
    background: linear-gradient(90deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.2) 20%);
}

#SpicyLyricsPage .ContentBox .NowBar:is(.Active.RightSide) + .LyricsContainer .loaderContainer {
    background: linear-gradient(270deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.2) 20%);
}

#SpicyLyricsPage .ContentBox.LyricsHidden .NowBar {
    margin: 0 !important;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox.Skeletoned {
    width: 20cqw;
    height: 20cqw;
    --BorderRadius: 1cqh;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .Header .MediaBox.Skeletoned {
    width: 30cqw;
    height: 30cqw;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox.Skeletoned .MediaImage {
    display: none;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .Metadata .SongName.Skeletoned {
    width: 14cqw;
    height: 4.5cqh;
    --BorderRadius: .25cqw;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox.Skeletoned * {
    display: none;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .Metadata .SongName.Skeletoned span {
    display: none;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .Metadata .Artists.Skeletoned {
    width: 12cqw;
    height: 3.5cqh;
    margin: 1.5cqh 0;
    --BorderRadius: .25cqw;
    /* margin: 1.5cqh 0;
    background: linear-gradient(300deg, rgba(255, 255, 255, 0.2) 25%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0.2) 75%);
    background-size: 200% 100%;
    border-radius: .45cqh;
    animation: shimmer 5s infinite;
    animation-delay: .7s;
    opacity: .6;
    box-shadow: 0 0 14px rgba(255, 255, 255, 0.12), 0 0 14px rgba(0, 0, 0, 0.1); */
}




#SpicyLyricsPage .ContentBox .NowBar .Header .Metadata .Artists.Skeletoned span {
    display: none;
}

/* #SpicyLyricsPage .ContentBox .NowBar:is(.Active.LeftSide) + .LyricsContainer .LyricsContent .line,
#SpicyLyricsPage .ContentBox .NowBar:is(.Active.LeftSide) + .LyricsContainer .LyricsContent .Credits {
    margin-left: 0 !important;
    margin-right: 10cqw !important;
}

#SpicyLyricsPage .ContentBox .NowBar:is(.Active.RightSide) + .LyricsContainer .LyricsContent .line,
#SpicyLyricsPage .ContentBox .NowBar:is(.Active.RightSide) + .LyricsContainer .LyricsContent .Credits {
    margin-left: 10cqw !important;
    margin-right: 0 !important;
}

#SpicyLyricsPage .ContentBox .NowBar:is(.Active.LeftSide) + .LyricsContainer .LyricsContent .line.OppositeAligned {
    margin-left: 0 !important;
    margin-right: 15cqw !important;
}

#SpicyLyricsPage .ContentBox .NowBar:is(.Active.RightSide) + .LyricsContainer .LyricsContent .line.OppositeAligned {
    margin-left: 15cqw !important;
    margin-right: 0 !important;
} */


#SpicyLyricsPage .ContentBox .DropZone.RightSide {
    order: 4;
}

#SpicyLyricsPage .ContentBox .DropZone.LeftSide {
    order: 0;
}

#SpicyLyricsPage .ContentBox .NowBar.LeftSide {
    order: 1;
}

#SpicyLyricsPage .ContentBox .LyricsContainer {
    order: 2;
}

#SpicyLyricsPage .ContentBox .NowBar.RightSide {
    order: 3;
}

#SpicyLyricsPage:has(.ContentBox .NowBar.Active.RightSide) .ScrollbarScrollable .simplebar-track.simplebar-vertical {
    left: 5px;
    right: 0;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .Header .MediaBox .MediaContent {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    display: flex;
    align-items: center;
    /* justify-content: space-between; */
    flex-direction: column;
    opacity: 0;
    transition: opacity .2s;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .Header .MediaBox:hover .MediaContent {
    opacity: .85;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox .MediaContent {
    display: none;
}

#SpicyLyricsPage .ContentBox .DropZone {
    width: 200cqw;
    height: 100cqh;
    position: absolute;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.25s ease-in-out;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

#SpicyLyricsPage .ContentBox .DropZone span {
    color: black;
    font-size: var(--default-font-size);
    text-align: center;
}

#SpicyLyricsPage .ContentBox .DropZone.Hidden {
    display: none !important;
}

#SpicyLyricsPage.SomethingDragging .ContentBox .LyricsContainer {
    display: none;
}

#SpicyLyricsPage.SomethingDragging .ContentBox .DropZone {
    position: relative; 
    z-index: 99999;
    opacity: .2;
}

#SpicyLyricsPage .ContentBox .NowBar .Header .MediaBox .MediaImage.Dragging {
    opacity: .6;
}

#SpicyLyricsPage.SomethingDragging .ContentBox .DropZone.DraggingOver {
    opacity: .5;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 5cqh;
    padding: 0 5cqh 0;
    /* margin-bottom: 3.8cqh; */
    position: absolute;
    bottom: 7.2cqh;
    z-index: 2;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl.TrackSkip.PrevTrack {
    rotate: 180deg;
}


/* #SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .Controls .PlaybackControl */

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl {
    aspect-ratio: 1;
    display: flex;
    fill: #fff;
    align-items: center;
    cursor: pointer;
    justify-content: center;
    transition: opacity .175s cubic-bezier(.37,0,.63,1), filter .175s ease-out; /* Added filter transition */
    --ShrinkScale: 0.9;
    --ShrinkDelta: calc(1 - var(--ShrinkScale));
    height: 4cqh;
    width: 4cqh;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl svg {
    transition: filter .175s ease-out; /* Added transition for SVG filter */
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl.ShuffleToggle,
#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl.LoopToggle {
    height: 2cqh;
    width: 2cqh;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl.PlayStateToggle.Playing {
   /*  margin-left: .248cqw; */
    height: 3.8cqh;
    width: 3.8cqh;
    /* margin-bottom: 1cqh; */
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl.PlayStateToggle.Paused {
    /* margin-left: .648cqw; */
    height: 4.4cqh;
    width: 4.4cqh;
    /* margin-bottom: .67cqh; */
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl:not(.Pressed) {
    animation: pressAnimation .6s;
    animation-fill-mode: forwards;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .ViewControls {
    opacity: 1 !important;
    position: relative;
    width: 100%;
    margin-bottom: 2cqh;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl.Pressed {
    transform: scale(var(--ShrinkScale));
    transition: opacity transform .175s cubic-bezier(.37, 0, .63, 1);
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls:hover .PlaybackControl:not(:hover) {
    opacity: .5;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl:hover {
    opacity: 1 !important;
}

#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl.ShuffleToggle.Enabled,
#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl.LoopToggle.Enabled {
    filter: brightness(2.75);
}

/* #SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl.ShuffleToggle.Enabled svg,
#SpicyLyricsPage.Fullscreen .ContentBox .NowBar .CenteredView .Header .MediaBox .MediaContent .PlaybackControls .PlaybackControl.LoopToggle.Enabled svg {
    filter: drop-shadow(0 0 5px white);
} */

@keyframes pressAnimation {
    0% {
        transform: scale(calc(1 - var(--ShrinkDelta)*1))
    }

    16% {
        transform: scale(calc(1 - var(--ShrinkDelta)*-.32))
    }

    28% {
        transform: scale(calc(1 - var(--ShrinkDelta)*.13))
    }

    44% {
        transform: scale(calc(1 - var(--ShrinkDelta)*-.05))
    }

    59% {
        transform: scale(calc(1 - var(--ShrinkDelta)*.02))
    }

    73% {
        transform: scale(calc(1 - var(--ShrinkDelta)*-.01))
    }

    88% {
        transform: scale(calc(1 - var(--ShrinkDelta)*0))
    }

    to {
        transform: scale(calc(1 - var(--ShrinkDelta)*0))
    }
}

#SpicyLyricsPage .Timeline {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    padding: 1.7cqw 2cqw;
    gap: .6cqw;
    position: absolute;
    bottom: 0;
}

#SpicyLyricsPage .Timeline .SliderBar {
    --TraveledColor: hsla(0,0%,100%,.9);
    --RemainingColor: hsla(0,0%,100%,.38);
    --SliderProgress: 0.6;
    background: linear-gradient(90deg,var(--TraveledColor) 0,var(--TraveledColor) calc(100%*var(--SliderProgress)),var(--RemainingColor) calc(100%*var(--SliderProgress)),var(--RemainingColor));
    border-radius: 100cqw;
    container-type: size;
    flex-grow: 1;
    position: relative;
    width: auto;
    height: .65cqh;
    cursor: pointer; /* Added to make it clickable */
}

#SpicyLyricsPage .Timeline .SliderBar .Handle {
    aspect-ratio: 1;
    background: #fff;
    border-radius: 100cqw;
    display: block;
    height: 185cqh;
    left: calc(100cqw*var(--SliderProgress));
    position: absolute;
    top: 54cqh;
    transform: translate(-50%,-50%)
}

#SpicyLyricsPage .NotificationContainer {
    display: none;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    inset: 0;
    z-index: 9999;
    width: 100%;
    height: 7cqh;
    padding: 2cqh 4cqh;
}

#SpicyLyricsPage .NotificationContainer.Danger {
    background: rgba(255, 118, 118, 0.5);
}

#SpicyLyricsPage .NotificationContainer.Information {
    background: rgba(158, 158, 255, 0.5);
}

#SpicyLyricsPage .NotificationContainer.Success {
    background: rgba(148, 255, 148, 0.5);
}

#SpicyLyricsPage .NotificationContainer.Warning {
    background: rgba(255, 208, 19, 0.5);
}

#SpicyLyricsPage .NotificationContainer.Visible {
    display: flex;
}

#SpicyLyricsPage .NotificationContainer .NotificationIcon {
    aspect-ratio: 1;
    width: 4cqh;
    height: 4cqh;
    display: flex;
    align-items: center;
    justify-content: center;
}

#SpicyLyricsPage .NotificationContainer .NotificationIcon svg {
    aspect-ratio: 1;
    width: 3cqh;
    height: 3cqh;
}

#SpicyLyricsPage .NotificationContainer.Danger .NotificationIcon svg {
    fill: #ff0000;
}

#SpicyLyricsPage .NotificationContainer.Information .NotificationIcon svg {
    fill: #2a2aff;
}

#SpicyLyricsPage .NotificationContainer.Success .NotificationIcon svg {
    fill: #00ff00;
}

#SpicyLyricsPage .NotificationContainer.Warning .NotificationIcon svg {
    fill: #ffaa00;
}

#SpicyLyricsPage .NotificationContainer .NotificationText {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin: 1cqh;
}

#SpicyLyricsPage .NotificationContainer .NotificationText .NotificationTitle {
    font-size: calc(var(--default-font-size) * 0.8);
    font-weight: 900;
    color: rgb(255, 255, 255);
    text-align: center;
}

#SpicyLyricsPage .NotificationContainer .NotificationText .NotificationDescription {
    font-size: calc(var(--default-font-size) * 0.4);
    font-weight: 400;
    color: rgb(206, 206, 206);
    text-align: center;
}

#SpicyLyricsPage .NotificationContainer .NotificationCloseButton {
    color: rgb(255, 255, 255);
    font-size: calc(var(--default-font-size) * 0.3);
    cursor: pointer;
    width: 4cqh;
    height: 4cqh;
    display: flex;
    align-items: center;
    justify-content: center;
}

#SpicyLyricsPage .NotificationContainer .NotificationCloseButton.Disabled {
    opacity: 0;
    z-index: -1;
    pointer-events: none;
    cursor: default;
}